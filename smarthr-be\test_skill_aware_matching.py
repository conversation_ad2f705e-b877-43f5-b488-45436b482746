#!/usr/bin/env python3
"""
Test script for the enhanced skill-aware matching system.
This script tests the new functionality with example scenarios.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.match_evaluations import (
    extract_position_skills_for_matching,
    evaluate_candidate_with_skill_priority,
    get_candidate_analysis_with_skill_priority
)
from models.SpecalizedOutputFormats.postionSkills import SkillImportance, SkillCategory


def test_salesforce_qa_example():
    """Test the Salesforce QA example mentioned by the user."""
    print("=" * 60)
    print("TESTING SALESFORCE QA POSITION")
    print("=" * 60)
    
    # Example Salesforce QA position description
    position_description = """
    Job Title: Salesforce QA Engineer
    
    We are seeking a skilled Quality Assurance Engineer with expertise in Salesforce platform testing.
    
    Key Responsibilities:
    - Design and execute comprehensive test plans for Salesforce applications
    - Perform manual and automated testing of Salesforce customizations
    - Test Salesforce integrations with external systems
    - Validate data migration and data quality in Salesforce
    - Collaborate with development teams on bug resolution
    - Create and maintain test documentation
    
    Required Skills:
    - 3+ years of experience in Quality Assurance/Software Testing
    - Strong knowledge of Salesforce platform (Sales Cloud, Service Cloud)
    - Experience with test automation tools (Selenium, TestComplete)
    - Manual testing expertise
    - Understanding of Salesforce configuration and customization
    - Knowledge of API testing (REST/SOAP)
    - Experience with Agile/Scrum methodologies
    
    Nice to Have:
    - Salesforce certifications (Administrator, Platform App Builder)
    - Experience with CI/CD pipelines
    - Knowledge of JavaScript and Apex
    """
    
    try:
        # Test skill extraction
        print("\n1. EXTRACTING PRIORITIZED SKILLS...")
        skills_analysis = extract_position_skills_for_matching(position_description)
        
        print(f"Position Title: {skills_analysis.position_title}")
        print(f"Primary Role Function: {skills_analysis.primary_role_function}")
        print(f"Skill Weight Rationale: {skills_analysis.skill_weight_rationale}")
        print("\nPrioritized Skills:")
        
        for skill in skills_analysis.prioritized_skills:
            print(f"  • {skill.name}")
            print(f"    - Importance: {skill.importance.value}")
            print(f"    - Category: {skill.category.value}")
            print(f"    - Weight: {skill.weight:.2f}")
            if skill.description:
                print(f"    - Description: {skill.description}")
            print()
        
        # Verify that QA skills have higher priority than Salesforce tools
        qa_skills = [s for s in skills_analysis.prioritized_skills 
                    if 'qa' in s.name.lower() or 'quality' in s.name.lower() or 'test' in s.name.lower()]
        salesforce_skills = [s for s in skills_analysis.prioritized_skills 
                           if 'salesforce' in s.name.lower()]
        
        print("VALIDATION RESULTS:")
        if qa_skills:
            max_qa_weight = max(s.weight for s in qa_skills)
            print(f"  ✓ QA skills found with max weight: {max_qa_weight:.2f}")
        else:
            print("  ✗ No QA skills identified")
            
        if salesforce_skills:
            max_sf_weight = max(s.weight for s in salesforce_skills)
            print(f"  ✓ Salesforce skills found with max weight: {max_sf_weight:.2f}")
        else:
            print("  ✗ No Salesforce skills identified")
            
        if qa_skills and salesforce_skills:
            max_qa_weight = max(s.weight for s in qa_skills)
            max_sf_weight = max(s.weight for s in salesforce_skills)
            if max_qa_weight > max_sf_weight:
                print("  ✓ SUCCESS: QA skills have higher weight than Salesforce skills")
            else:
                print("  ⚠ WARNING: Salesforce skills have equal or higher weight than QA skills")
        
        return skills_analysis
        
    except Exception as e:
        print(f"ERROR in skill extraction: {str(e)}")
        return None


def test_candidate_matching():
    """Test candidate matching with skill prioritization."""
    print("\n" + "=" * 60)
    print("TESTING CANDIDATE MATCHING")
    print("=" * 60)
    
    # Example candidate profiles
    good_qa_candidate = """
    John Smith - Senior QA Engineer
    
    Experience:
    - 5 years of Quality Assurance experience in enterprise software
    - Expert in manual and automated testing methodologies
    - Strong experience with Selenium WebDriver and TestNG
    - Proficient in API testing using Postman and REST Assured
    - Experience with Salesforce testing and configuration
    - Knowledge of Agile/Scrum practices
    - Bug tracking and test case management tools (JIRA, TestRail)
    
    Skills:
    - Quality Assurance, Test Automation, Manual Testing
    - Salesforce Platform, Sales Cloud, Service Cloud
    - Selenium, TestNG, API Testing
    - Agile, Scrum, JIRA
    """
    
    salesforce_developer_candidate = """
    Jane Doe - Salesforce Developer
    
    Experience:
    - 4 years of Salesforce development experience
    - Expert in Apex, Visualforce, and Lightning components
    - Strong knowledge of Salesforce configuration and customization
    - Experience with Salesforce integrations and APIs
    - Some exposure to testing Salesforce applications
    - Salesforce Certified Platform Developer I & II
    
    Skills:
    - Salesforce Development, Apex, Visualforce, Lightning
    - Salesforce Configuration, Process Builder, Workflows
    - REST/SOAP APIs, Integration patterns
    - Basic testing knowledge
    """
    
    position_description = """
    Job Title: Salesforce QA Engineer
    
    We are seeking a skilled Quality Assurance Engineer with expertise in Salesforce platform testing.
    
    Required Skills:
    - 3+ years of experience in Quality Assurance/Software Testing
    - Strong knowledge of Salesforce platform (Sales Cloud, Service Cloud)
    - Experience with test automation tools (Selenium, TestComplete)
    - Manual testing expertise
    - Understanding of Salesforce configuration and customization
    """
    
    try:
        print("\n1. TESTING GOOD QA CANDIDATE...")
        qa_result = evaluate_candidate_with_skill_priority(
            good_qa_candidate, position_description
        )
        qa_score = qa_result.get("Score", 0)
        print(f"QA Candidate Score: {qa_score}")
        print(f"Analysis Reason: {qa_result.get('LLM_Analysis', {}).get('reason', 'N/A')[:200]}...")
        
        print("\n2. TESTING SALESFORCE DEVELOPER CANDIDATE...")
        dev_result = evaluate_candidate_with_skill_priority(
            salesforce_developer_candidate, position_description
        )
        dev_score = dev_result.get("Score", 0)
        print(f"Developer Candidate Score: {dev_score}")
        print(f"Analysis Reason: {dev_result.get('LLM_Analysis', {}).get('reason', 'N/A')[:200]}...")
        
        print("\nVALIDATION RESULTS:")
        if qa_score > dev_score:
            print(f"  ✓ SUCCESS: QA candidate scored higher ({qa_score}) than Developer candidate ({dev_score})")
        else:
            print(f"  ⚠ WARNING: Developer candidate scored equal or higher ({dev_score}) than QA candidate ({qa_score})")
            
        return {"qa_score": qa_score, "dev_score": dev_score}
        
    except Exception as e:
        print(f"ERROR in candidate matching: {str(e)}")
        return None


def main():
    """Run all tests."""
    print("SKILL-AWARE MATCHING SYSTEM TEST")
    print("This test validates that the system correctly prioritizes skills")
    print("For a Salesforce QA position, QA skills should be weighted higher than Salesforce tools")
    
    # Test 1: Skill extraction
    skills_result = test_salesforce_qa_example()
    
    # Test 2: Candidate matching
    matching_result = test_candidate_matching()
    
    print("\n" + "=" * 60)
    print("OVERALL TEST RESULTS")
    print("=" * 60)
    
    if skills_result and matching_result:
        print("✓ All tests completed successfully")
        print("✓ Skill-aware matching system is working correctly")
    else:
        print("✗ Some tests failed - check the error messages above")


if __name__ == "__main__":
    main()
