# Skill-Aware Matching System - User Guide

## Overview
The enhanced matching system now provides intelligent skill prioritization that replaces the old role-type matching approach. For positions like "Salesforce QA Engineer", QA skills are now weighted higher than Salesforce tools, exactly as requested.

## Key Differences

### OLD System (Role-Type Matching)
- Applied rigid role-type caps (e.g., Developer → QA = max 35%)
- Used fixed 60% role function / 40% technology weighting
- Penalized candidates for role mismatches regardless of relevant skills

### NEW System (Skill-Aware Matching)
- Dynamically identifies most important skills for each position
- Weights skills based on job requirements (e.g., QA skills > Salesforce tools for QA roles)
- Uses 70% skill-based evaluation / 30% role function alignment
- No arbitrary role-type caps - focuses on actual skill relevance

## Available Endpoints

### New Skill-Aware Endpoints (Use These!)
1. **`/evaluate_candidate_with_skill_priority`**
   - POST endpoint for skill-aware candidate evaluation
   - Parameters: `candidate_text`, `position_text`
   - Returns: Enhanced analysis with skill prioritization

2. **`/evaluate_candidate_custom_prompt_with_skill_priority`**
   - POST endpoint for skill-aware custom prompt evaluation
   - Parameters: `candidate_text`, `processed_position`
   - Returns: CompatibilityEvaluation with skill prioritization

3. **`/extract_position_skills`**
   - POST endpoint to extract prioritized skills from job descriptions
   - Parameters: `position_text`
   - Returns: PositionSkillAnalysis with weighted skills

4. **`/test_llm_match_with_skill_priority`**
   - GET endpoint to compare old vs new matching approaches
   - Parameters: `model_name`, `position_id`, `candidate_id`

### Old Endpoints (Still Available for Backward Compatibility)
- `/evaluate_candidate` - Uses old role-type matching
- `/evaluate_candidate_custom_prompt` - Uses old role-type matching
- `/evaluate_candidates_batch` - Uses old role-type matching

## Example: Salesforce QA Position

For a "Salesforce QA Engineer" position, the new system will:

1. **Extract Prioritized Skills:**
   - Quality Assurance (critical, weight: 0.90) - Primary job function
   - Test Automation (critical, weight: 0.80) - Essential for modern QA
   - Salesforce Platform (important, weight: 0.60) - Secondary to QA skills
   - Manual Testing (important, weight: 0.50) - Valuable but less critical

2. **Evaluate Candidates Based on Skills:**
   - QA candidate with extensive testing experience: High score (focuses on QA skills)
   - Salesforce developer with minimal QA: Lower score (lacks primary skills)
   - No arbitrary role-type caps applied

## Testing Results

Our test validation shows:
- ✅ QA skills correctly weighted higher (0.90) than Salesforce skills (0.60-0.80)
- ✅ Skill-aware matching prioritizes relevant experience over role titles
- ✅ System properly identifies and weights the most important skills for each position

## Migration Guide

To use the new skill-aware matching:

1. **Replace old endpoint calls:**
   ```
   OLD: POST /evaluate_candidate
   NEW: POST /evaluate_candidate_with_skill_priority
   ```

2. **Update your application logic:**
   - The new system focuses on skill relevance rather than role-type matching
   - Candidates are evaluated based on actual skill alignment, not job titles
   - Scoring is more nuanced and context-aware

3. **Expect different results:**
   - QA candidates will score higher for QA positions regardless of technology stack
   - Technology specialists will score higher when their skills are truly critical
   - More accurate matching based on actual job requirements

## Support

If you're still seeing role-type caps (35%, 55%, etc.), you're likely using the old endpoints. Switch to the skill-aware endpoints listed above for the enhanced matching behavior.
