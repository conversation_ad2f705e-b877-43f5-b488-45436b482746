#!/usr/bin/env python3
"""
Demonstration script showing the difference between old role-type matching 
and new skill-aware matching for Salesforce QA positions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.match_evaluations import (
    evaluate_candidate,  # Old role-type matching
    evaluate_candidate_with_skill_priority,  # New skill-aware matching
    extract_position_skills_for_matching
)

def main():
    print("=" * 80)
    print("SKILL-AWARE vs OLD MATCHING COMPARISON")
    print("Demonstrating why QA candidates should score higher than developers for QA roles")
    print("=" * 80)
    
    # Test position
    position = """
    Job Title: Salesforce QA Engineer
    
    We are seeking a skilled Quality Assurance Engineer with expertise in Salesforce platform testing.
    
    Key Responsibilities:
    - Design and execute comprehensive test plans for Salesforce applications
    - Perform manual and automated testing of Salesforce customizations
    - Test Salesforce integrations with external systems
    - Validate data migration and data quality in Salesforce
    - Collaborate with development teams on bug resolution
    - Create and maintain test documentation
    
    Required Skills:
    - 3+ years of experience in Quality Assurance/Software Testing
    - Strong knowledge of Salesforce platform (Sales Cloud, Service Cloud)
    - Experience with test automation tools (Selenium, TestComplete)
    - Manual testing expertise
    - Understanding of Salesforce configuration and customization
    - Knowledge of API testing (REST/SOAP)
    - Experience with Agile/Scrum methodologies
    
    Nice to Have:
    - Salesforce certifications (Administrator, Platform App Builder)
    - Experience with CI/CD pipelines
    - Knowledge of JavaScript and Apex
    """
    
    # QA Candidate - should score higher for QA role
    qa_candidate = """
    John Smith - Senior QA Engineer
    
    Experience:
    - 5 years of Quality Assurance experience in enterprise software
    - Expert in manual and automated testing methodologies
    - Strong experience with Selenium WebDriver and TestNG
    - Proficient in API testing using Postman and REST Assured
    - Experience with test case design and test planning
    - Knowledge of Agile/Scrum practices
    - Bug tracking and test case management tools (JIRA, TestRail)
    - Some exposure to Salesforce testing
    
    Skills:
    - Quality Assurance, Test Automation, Manual Testing
    - Selenium, TestNG, API Testing, Test Planning
    - Agile, Scrum, JIRA, TestRail
    - Basic Salesforce knowledge
    """
    
    # Salesforce Developer - should score lower for QA role
    sf_developer = """
    Jane Doe - Salesforce Developer
    
    Experience:
    - 4 years of Salesforce development experience
    - Expert in Apex, Visualforce, and Lightning components
    - Strong knowledge of Salesforce configuration and customization
    - Experience with Salesforce integrations and APIs
    - Minimal testing experience (unit tests only)
    - Salesforce Certified Platform Developer I & II
    
    Skills:
    - Salesforce Development, Apex, Visualforce, Lightning
    - Salesforce Configuration, Process Builder, Workflows
    - REST/SOAP APIs, Integration patterns
    - Basic unit testing knowledge
    """
    
    print("\n1. EXTRACTING PRIORITIZED SKILLS FROM POSITION...")
    print("-" * 50)
    
    try:
        skills_analysis = extract_position_skills_for_matching(position)
        print(f"Position: {skills_analysis.position_title}")
        print(f"Primary Role Function: {skills_analysis.primary_role_function}")
        print("\nPrioritized Skills:")
        for skill in skills_analysis.prioritized_skills:
            print(f"  • {skill.name}")
            print(f"    - Importance: {skill.importance}")
            print(f"    - Category: {skill.category}")
            print(f"    - Weight: {skill.weight}")
            print()
    except Exception as e:
        print(f"Error extracting skills: {e}")
        return
    
    print("\n2. COMPARING MATCHING APPROACHES...")
    print("-" * 50)
    
    # Test QA Candidate
    print("\nQA CANDIDATE EVALUATION:")
    print("=" * 30)
    
    try:
        # Old matching
        old_result = evaluate_candidate(qa_candidate, position)
        old_score = old_result.get('Score', 0)
        old_reason = old_result.get('LLM_Analysis', {}).get('reason', 'No reason provided')
        
        print(f"OLD System Score: {old_score}")
        print(f"OLD System Reasoning: {old_reason[:200]}...")
        
        # New skill-aware matching
        new_result = evaluate_candidate_with_skill_priority(qa_candidate, position)
        new_score = new_result.get('Score', 0)
        new_reason = new_result.get('LLM_Analysis', {}).get('reason', 'No reason provided')
        
        print(f"\nNEW System Score: {new_score}")
        print(f"NEW System Reasoning: {new_reason[:200]}...")
        
    except Exception as e:
        print(f"Error evaluating QA candidate: {e}")
    
    # Test Salesforce Developer
    print("\n\nSALESFORCE DEVELOPER EVALUATION:")
    print("=" * 35)
    
    try:
        # Old matching
        old_result = evaluate_candidate(sf_developer, position)
        old_score = old_result.get('Score', 0)
        old_reason = old_result.get('LLM_Analysis', {}).get('reason', 'No reason provided')
        
        print(f"OLD System Score: {old_score}")
        print(f"OLD System Reasoning: {old_reason[:200]}...")
        
        # New skill-aware matching
        new_result = evaluate_candidate_with_skill_priority(sf_developer, position)
        new_score = new_result.get('Score', 0)
        new_reason = new_result.get('LLM_Analysis', {}).get('reason', 'No reason provided')
        
        print(f"\nNEW System Score: {new_score}")
        print(f"NEW System Reasoning: {new_reason[:200]}...")
        
    except Exception as e:
        print(f"Error evaluating Salesforce developer: {e}")
    
    print("\n" + "=" * 80)
    print("SUMMARY:")
    print("- OLD system applies role-type caps (Developer → QA = max 35%)")
    print("- NEW system prioritizes QA skills over Salesforce tools for QA positions")
    print("- QA candidate should score higher with NEW system")
    print("- Use skill-aware endpoints for better matching results")
    print("=" * 80)

if __name__ == "__main__":
    main()
