# match_evaluation.py
from datetime import datetime
from typing import Any, Dict, List
from config.config import MODELS_CONFIG
from models.llm import inference_with_fallback, get_related_class_definitions
from langchain_core.messages import HumanMessage
from opentelemetry.trace import Status, StatusCode
from models.llm import models_pool
from models.match_analysis_models import (
    CompatibilityEvaluation,
    PositionCandidateAnalysis,
    BatchMatchAnalysis
)
from contextlib import contextmanager
import time
# Note: LangSmith-based prompts replaced with enhanced prompts that include proper technical skills weighting
# from templates.candidates_templates.candidate_analysis import (
#     get_candidate_analysis_prompt,
#     get_batch_candidate_analysis_batch_prompt,
# )
from templates.positions_templates.position_analysis import get_position_analysis_prompt
import logging
from opentelemetry import trace
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)


@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


def get_enhanced_candidate_analysis_prompt() -> str:
    """
    Enhanced prompt template that incorporates proper technical skills weighting
    while maintaining PositionCandidateAnalysis output format.
    """
    return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach with proper technical skills weighting.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential (can score 70-100%)
   - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
   - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**COMPATIBILITY CALCULATION RULES:**
- Final Score = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- **MANDATORY CAPS based on role type mismatch:**
  * Major role mismatch (e.g., Developer vs QA): **MAXIMUM 35%** regardless of technology overlap
  * Moderate role mismatch (e.g., Frontend vs Backend): **MAXIMUM 55%**
  * Minor role mismatch (e.g., Junior vs Senior same role): **MAXIMUM 75%**
  * Same role type: Up to 100% based on technology and experience alignment

**CRITICAL EXAMPLES:**
- Salesforce Developer → Salesforce QA Position: MAX 35% (same technology, completely different job functions)
- React Developer → Angular Developer Position: MAX 75% (same role type, different but related technology)
- Python Developer → Python Developer Position: Up to 100% (perfect alignment)
- Manual QA → Automation QA Position: MAX 75% (same domain, different approach)

**OUTPUT REQUIREMENTS:**
Return a JSON response with EXACTLY this structure (do not nest extra_questions, highlights, or Score inside LLM_Analysis):

{
  "LLM_Analysis": {
    "reason": "Detailed explanation of the compatibility assessment including role type analysis and weighting applied",
    "skill_match_analysis": {"skill_name": "explanation for matched skills only"},
    "skill_not_matched": ["list of skills not matched"]
  },
  "extra_questions": ["list of additional questions for further evaluation"],
  "highlights": ["list of key points about the candidate's suitability"],
  "Score": 85.5
}

**CRITICAL:**
- The Score field must be at the TOP LEVEL, not inside LLM_Analysis
- The extra_questions field must be at the TOP LEVEL, not inside LLM_Analysis
- The highlights field must be at the TOP LEVEL, not inside LLM_Analysis
- The Score must reflect the proper weighting (60% role function, 40% technology skills) and respect the maximum caps for role type mismatches
- In the reason field, explicitly mention the role type assessment and any compatibility caps applied"""


def evaluate_candidate(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> dict:
    with tracer.start_as_current_span("evaluate_candidate_llm") as span:
        span.set_attribute("eval.candidate_length", len(candidate))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando candidato con LLM usando prompt mejorado con ponderación de habilidades técnicas")
        system_prompt = get_enhanced_candidate_analysis_prompt()

        messages = [
            {"role": "user", "content": f"Candidate description: {candidate}"},
            {"role": "user", "content": f"Position description: {position}"},
        ]
        analysis_response = {}

        with log_time_block("Invoke LLM on evaluate candidate"):
            analysis_response = inference_with_fallback(
                task_prompt=system_prompt,
                model_schema=PositionCandidateAnalysis,
                user_messages=messages,
                models_order=models_order
            )
        if not analysis_response:
            logger.error("LLM analysis failed for candidate evaluation")
            return {
                "LLM_Analysis": {
                    "reason": "LLM analysis failed",
                    "skill_match_analysis": {},
                    "skill_not_matched": []
                },
                "extra_questions": [],
                "highlights": [],
                "Score": 0.0,
            }
        analysis_response = analysis_response.model_dump()
        score = analysis_response.get("Score", 0.0)
        try:
            score = float(score)
        except ValueError:
            score = 0.0

        logger.info(
            "Respuesta del LLM procesada con ponderación mejorada",
            extra={"custom_dimensions": {"score": score}}
        )
        return analysis_response


def evaluate_position(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> PositionCandidateAnalysis:
    """
    Evaluate a position for a given candidate using the LLM.
    Uses template from position_analysis.py
    """
    system_prompt = get_position_analysis_prompt()

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Candidate description: {candidate}"},
        {"role": "user", "content": f"Position description: {position}"},
        # {"role": "user", "content": f"Initial similarity score: {initial_score}"}
    ]

    # Attempt models in order
    default_response = {
        "LLM_Analysis": {},
        "extra_questions": {},
        "highlights": {},
        "Score": 0.0,
    }

    analysis_response = _invoke_llm_with_fallback(
        messages, default_response, model_order=models_order
    )

    # Ensure Score is float
    score = analysis_response.get("Score", 0.0)
    try:
        score = float(score)
    except ValueError:
        score = 0.0

    return PositionCandidateAnalysis(
        LLM_Analysis=analysis_response.get("LLM_Analysis", {}),
        extra_questions=analysis_response.get("extra_questions", {}),
        highlights=analysis_response.get("highlights", {}),
        Score=score,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def get_enhanced_batch_candidate_analysis_prompt() -> str:
    """
    Enhanced batch prompt template that incorporates proper technical skills weighting
    for multiple candidates evaluation.
    """
    return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and multiple candidates' CVs using a two-factor assessment approach with proper technical skills weighting.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and each candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential (can score 70-100%)
   - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
   - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**COMPATIBILITY CALCULATION RULES:**
- Final Score = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- **MANDATORY CAPS based on role type mismatch:**
  * Major role mismatch (e.g., Developer vs QA): **MAXIMUM 35%** regardless of technology overlap
  * Moderate role mismatch (e.g., Frontend vs Backend): **MAXIMUM 55%**
  * Minor role mismatch (e.g., Junior vs Senior same role): **MAXIMUM 75%**
  * Same role type: Up to 100% based on technology and experience alignment

**OUTPUT REQUIREMENTS:**
Return a JSON object with "candidates_analysis" array containing analysis for each candidate in order.

Each candidate analysis must have EXACTLY this structure (do not nest extra_questions, highlights, or Score inside LLM_Analysis):

{
  "candidates_analysis": [
    {
      "LLM_Analysis": {
        "reason": "Detailed explanation including role type analysis and weighting applied",
        "skill_match_analysis": {"skill_name": "explanation for matched skills only"},
        "skill_not_matched": ["list of skills not matched"]
      },
      "extra_questions": ["list of additional questions"],
      "highlights": ["list of key points about suitability"],
      "Score": 85.5
    }
  ]
}

**CRITICAL:**
- The Score field must be at the TOP LEVEL of each candidate analysis, not inside LLM_Analysis
- The extra_questions field must be at the TOP LEVEL, not inside LLM_Analysis
- The highlights field must be at the TOP LEVEL, not inside LLM_Analysis
- Each Score must reflect the proper weighting (60% role function, 40% technology skills) and respect the maximum caps for role type mismatches
- In each reason field, explicitly mention the role type assessment and any compatibility caps applied"""


def evaluate_candidates_batch(candidates: List[str], position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> BatchMatchAnalysis:
    """
    Evaluate multiple candidates against a position in a single LLM call.
    Returns a BatchMatchAnalysis with individual analyses and overall summary.
    """
    with tracer.start_as_current_span("evaluate_candidates_batch_llm") as span:
        span.set_attribute("eval.candidates_count", len(candidates))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando %d candidatos en modo batch con ponderación mejorada de habilidades técnicas", len(candidates))

        task_prompt = get_enhanced_batch_candidate_analysis_prompt()

        # Prepare candidates for batch analysis
        candidates_prompt = "\n".join([
            f"[Init Candidate] Candidate {i + 1}:\n{candidate} [End of Candidate]" 
            for i, candidate in enumerate(candidates)
        ])

        # Create user messages
        user_messages = [
            HumanMessage(content=f"Position Description:\n{position}"),
            HumanMessage(content=f"Candidates to Evaluate:\n{candidates_prompt}")
        ]

        # Get schema text for BatchMatchAnalysis
        schema_text = """
 

class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")

class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    ) #
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.") #

class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
        """

        with log_time_block("Batch LLM Analysis"):
            try:
                # Use inference_with_fallback with structured output
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=BatchMatchAnalysis,
                    user_messages=user_messages,
                    model_schema_text=schema_text,
                    models_order=models_order
                )

                if not result:
                    raise RuntimeError("All LLM providers failed")

                # Validate and normalize scores
                for analysis in result.candidates_analysis:
                    try:
                        score = float(analysis.Score)
                        score = max(0.0, min(100.0, score))  # Clamp between 0 and 100
                        analysis.Score = score
                    except (ValueError, TypeError):
                        analysis.Score = 0.0

                logger.info(
                    "Batch analysis completed successfully",
                    extra={
                        "custom_dimensions": {
                            "candidates_count": len(candidates),
                            "analysis_count": len(result.candidates_analysis)
                        }
                    }
                )

                return result

            except Exception as e:
                logger.error("Error in batch analysis: %s", str(e), exc_info=True)
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR))
                raise RuntimeError(f"Batch analysis failed: {str(e)}")


def get_candidate_analysis_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    # 1) Build prompt
    task_prompt = (
        f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach.

        **CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

        **ASSESSMENT METHODOLOGY:**
        1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and candidate's background.
           - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
           - **Same role type**: High compatibility potential (can score 70-100%)
           - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
           - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

        2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

        **COMPATIBILITY CALCULATION RULES:**
        - Final Compatibility = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
        - **MANDATORY CAPS based on role type mismatch:**
          * Major role mismatch (e.g., Developer vs QA): **MAXIMUM 35%** regardless of technology overlap
          * Moderate role mismatch (e.g., Frontend vs Backend): **MAXIMUM 55%**
          * Minor role mismatch (e.g., Junior vs Senior same role): **MAXIMUM 75%**
          * Same role type: Up to 100% based on technology and experience alignment

        **CRITICAL EXAMPLES:**
        - Salesforce Developer → Salesforce QA Position: MAX 35% (same technology, completely different job functions)
        - React Developer → Angular Developer Position: MAX 75% (same role type, different but related technology)
        - Python Developer → Python Developer Position: Up to 100% (perfect alignment)
        - Manual QA → Automation QA Position: MAX 75% (same domain, different approach)

        **ANALYSIS REQUIREMENTS:**
        1. Percentage of compatibility with the position (applying the role type compatibility rules above)
        2. Recommendation: Whether the candidate should move forward (consider both role fit and technology alignment)
        3. Matches Found: Points where candidate meets requirements (distinguish between role-relevant and technology matches)
        4. Missing: Key requirements not evident in candidate's background (prioritize role function gaps over technology gaps)

        I will provide you with the Job Description and CV for analysis.\n\n"""
        f"""Job Description:\n{processed_position}\n\n"""
        f"""CV:\n{candidate_text}\n\n"""
        f"""Return the analysis in strict JSON format according to the CompatibilityEvaluation model, containing the following required fields:
        - compatibilityPercentage (float with one decimal place between 0.0 and 100.0, e.g., 83.7, 42.6 — avoid round numbers or multiples of 5)
        - recommendation (boolean)
        - justification (string - must explain role type compatibility assessment and any caps applied)
        - matchesFound (array of strings)
        - missing (array of strings)

        Requirements:
        1. The field compatibilityPercentage **must be a float, not an integer**, and must include **exactly one decimal place** (e.g., 87.3, 46.8).
        2. **Do not round or simplify** compatibilityPercentage to multiples of 5 or integers.
        3. **MANDATORY**: Apply the role type compatibility caps - do not exceed maximum percentages for role mismatches.
        4. All fields must be filled, even if matchesFound or missing are empty arrays.
        5. Output must be valid JSON — do not include explanations or extra formatting.
        6. In justification, explicitly mention the role type assessment and any compatibility caps applied.
        """
    )
    schema_text = get_related_class_definitions(CompatibilityEvaluation)

    # 2) Call the inference function with the task prompt
    # Using inference_with_fallback to handle multiple models and fallbacks
    analysis = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=CompatibilityEvaluation,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if analysis is None or empty
    if not analysis:
        return None
    return analysis


def get_roles_to_candidates(candidate: dict) -> List[str]:
    # 1) Build prompt
    task_prompt = (
        f"""From the candidate’s resume, output exactly five specific job titles the candidate is qualified to apply for. Return only a JSON array of strings with the job titles (e.g., ["Senior .NET Developer", "..."]). Do not include any explanations or additional fields."""
        f"""\n\nCandidate Resume:\n{candidate}\n\n"""
    )
    # 2) Call the inference function with the task prompt
    roles = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=dict,  # Expecting a simple list of strings
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if roles is None or empty
    if not roles:
        return None
    return roles.get("job_titles", [])


def _invoke_llm_with_fallback(
    messages, default_response, model_order: list = MODELS_CONFIG["default_models_order"]
) -> Dict[str, Any]:
    """
    Attempt to invoke LLM models in order until one succeeds.
    Return default_response if all fail.
    The model is expected to return JSON with required fields.
    """
    print("----------------Evaluating in invoke llm with fallback--------------")
    print(model_order)
    print("=====default_response========")
    print(default_response)
    print("-----------------------------------------------------")
    for model_name in model_order:
        try:
            structured_llm = models_pool[model_name].with_structured_output(
                default_response, method="json_mode"
            )
            result = structured_llm.invoke(messages).model_dump()
            # Validate result is JSON with required keys
            if isinstance(result, dict) and all(
                k in result
                for k in ["LLM_Analysis", "extra_questions", "highlights", "Score"]
            ):
                return result
        except Exception as e:
            print(f"Model {model_name} failed: {e}")

    return default_response


def build_compatibility_evaluation(
    compatibility_percentage: float,
    justification: str,
    matches_found: List[str],
    missing_requirements: List[str]
) -> CompatibilityEvaluation:
    """
    Builds a CompatibilityEvaluation object with the provided parameters.
    """
    return CompatibilityEvaluation(
        compatibilityPercentage=compatibility_percentage,
        recommendation=compatibility_percentage >= 65.0,
        justification=justification,
        matchesFound=matches_found,
        missing=missing_requirements        
    )