# match_evaluation.py
from datetime import datetime
from typing import Any, Dict, List
from config.config import MODELS_CONFIG
from models.llm import inference_with_fallback, get_related_class_definitions
from langchain_core.messages import HumanMessage
from opentelemetry.trace import Status, StatusCode
from models.llm import models_pool
from models.match_analysis_models import (
    CompatibilityEvaluation,
    PositionCandidateAnalysis,
    BatchMatchAnalysis
)
from models.SpecalizedOutputFormats.postionSkills import PositionSkillAnalysis
from contextlib import contextmanager
import time
# Note: LangSmith-based prompts replaced with enhanced prompts that include proper technical skills weighting
# from templates.candidates_templates.candidate_analysis import (
#     get_candidate_analysis_prompt,
#     get_batch_candidate_analysis_batch_prompt,
# )
from templates.positions_templates.position_analysis import get_position_analysis_prompt
import logging
from opentelemetry import trace
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)


@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


def get_enhanced_candidate_analysis_prompt() -> str:
    """
    Enhanced prompt template that incorporates proper technical skills weighting
    while maintaining PositionCandidateAnalysis output format.
    """
    return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach with proper technical skills weighting.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential (can score 70-100%)
   - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
   - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**COMPATIBILITY CALCULATION RULES:**
- Final Score = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- **MANDATORY CAPS based on role type mismatch:**
  * Major role mismatch (e.g., Developer vs QA): **MAXIMUM 35%** regardless of technology overlap
  * Moderate role mismatch (e.g., Frontend vs Backend): **MAXIMUM 55%**
  * Minor role mismatch (e.g., Junior vs Senior same role): **MAXIMUM 75%**
  * Same role type: Up to 100% based on technology and experience alignment

**CRITICAL EXAMPLES:**
- Salesforce Developer → Salesforce QA Position: MAX 35% (same technology, completely different job functions)
- React Developer → Angular Developer Position: MAX 75% (same role type, different but related technology)
- Python Developer → Python Developer Position: Up to 100% (perfect alignment)
- Manual QA → Automation QA Position: MAX 75% (same domain, different approach)

**OUTPUT REQUIREMENTS:**
Return a JSON response with EXACTLY this structure (do not nest extra_questions, highlights, or Score inside LLM_Analysis):

{
  "LLM_Analysis": {
    "reason": "Detailed explanation of the compatibility assessment including role type analysis and weighting applied",
    "skill_match_analysis": {"skill_name": "explanation for matched skills only"},
    "skill_not_matched": ["list of skills not matched"]
  },
  "extra_questions": ["list of additional questions for further evaluation"],
  "highlights": ["list of key points about the candidate's suitability"],
  "Score": 85.5
}

**CRITICAL:**
- The Score field must be at the TOP LEVEL, not inside LLM_Analysis
- The extra_questions field must be at the TOP LEVEL, not inside LLM_Analysis
- The highlights field must be at the TOP LEVEL, not inside LLM_Analysis
- The Score must reflect the proper weighting (60% role function, 40% technology skills) and respect the maximum caps for role type mismatches
- In the reason field, explicitly mention the role type assessment and any compatibility caps applied"""


def evaluate_candidate(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> dict:
    with tracer.start_as_current_span("evaluate_candidate_llm") as span:
        span.set_attribute("eval.candidate_length", len(candidate))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando candidato con LLM usando prompt mejorado con ponderación de habilidades técnicas")
        system_prompt = get_enhanced_candidate_analysis_prompt()

        messages = [
            {"role": "user", "content": f"Candidate description: {candidate}"},
            {"role": "user", "content": f"Position description: {position}"},
        ]
        analysis_response = {}

        with log_time_block("Invoke LLM on evaluate candidate"):
            analysis_response = inference_with_fallback(
                task_prompt=system_prompt,
                model_schema=PositionCandidateAnalysis,
                user_messages=messages,
                models_order=models_order
            )
        if not analysis_response:
            logger.error("LLM analysis failed for candidate evaluation")
            return {
                "LLM_Analysis": {
                    "reason": "LLM analysis failed",
                    "skill_match_analysis": {},
                    "skill_not_matched": []
                },
                "extra_questions": [],
                "highlights": [],
                "Score": 0.0,
            }
        analysis_response = analysis_response.model_dump()
        score = analysis_response.get("Score", 0.0)
        try:
            score = float(score)
        except ValueError:
            score = 0.0

        logger.info(
            "Respuesta del LLM procesada con ponderación mejorada",
            extra={"custom_dimensions": {"score": score}}
        )
        return analysis_response


def evaluate_position(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> PositionCandidateAnalysis:
    """
    Evaluate a position for a given candidate using the LLM.
    Uses template from position_analysis.py
    """
    system_prompt = get_position_analysis_prompt()

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Candidate description: {candidate}"},
        {"role": "user", "content": f"Position description: {position}"},
        # {"role": "user", "content": f"Initial similarity score: {initial_score}"}
    ]

    # Attempt models in order
    default_response = {
        "LLM_Analysis": {},
        "extra_questions": {},
        "highlights": {},
        "Score": 0.0,
    }

    analysis_response = _invoke_llm_with_fallback(
        messages, default_response, model_order=models_order
    )

    # Ensure Score is float
    score = analysis_response.get("Score", 0.0)
    try:
        score = float(score)
    except ValueError:
        score = 0.0

    return PositionCandidateAnalysis(
        LLM_Analysis=analysis_response.get("LLM_Analysis", {}),
        extra_questions=analysis_response.get("extra_questions", {}),
        highlights=analysis_response.get("highlights", {}),
        Score=score,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def get_enhanced_batch_candidate_analysis_prompt() -> str:
    """
    Enhanced batch prompt template that incorporates proper technical skills weighting
    for multiple candidates evaluation.
    """
    return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and multiple candidates' CVs using a two-factor assessment approach with proper technical skills weighting.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and each candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential (can score 70-100%)
   - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
   - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**COMPATIBILITY CALCULATION RULES:**
- Final Score = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- **MANDATORY CAPS based on role type mismatch:**
  * Major role mismatch (e.g., Developer vs QA): **MAXIMUM 35%** regardless of technology overlap
  * Moderate role mismatch (e.g., Frontend vs Backend): **MAXIMUM 55%**
  * Minor role mismatch (e.g., Junior vs Senior same role): **MAXIMUM 75%**
  * Same role type: Up to 100% based on technology and experience alignment

**OUTPUT REQUIREMENTS:**
Return a JSON object with "candidates_analysis" array containing analysis for each candidate in order.

Each candidate analysis must have EXACTLY this structure (do not nest extra_questions, highlights, or Score inside LLM_Analysis):

{
  "candidates_analysis": [
    {
      "LLM_Analysis": {
        "reason": "Detailed explanation including role type analysis and weighting applied",
        "skill_match_analysis": {"skill_name": "explanation for matched skills only"},
        "skill_not_matched": ["list of skills not matched"]
      },
      "extra_questions": ["list of additional questions"],
      "highlights": ["list of key points about suitability"],
      "Score": 85.5
    }
  ]
}

**CRITICAL:**
- The Score field must be at the TOP LEVEL of each candidate analysis, not inside LLM_Analysis
- The extra_questions field must be at the TOP LEVEL, not inside LLM_Analysis
- The highlights field must be at the TOP LEVEL, not inside LLM_Analysis
- Each Score must reflect the proper weighting (60% role function, 40% technology skills) and respect the maximum caps for role type mismatches
- In each reason field, explicitly mention the role type assessment and any compatibility caps applied"""


def evaluate_candidates_batch(candidates: List[str], position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> BatchMatchAnalysis:
    """
    Evaluate multiple candidates against a position in a single LLM call.
    Returns a BatchMatchAnalysis with individual analyses and overall summary.
    """
    with tracer.start_as_current_span("evaluate_candidates_batch_llm") as span:
        span.set_attribute("eval.candidates_count", len(candidates))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando %d candidatos en modo batch con ponderación mejorada de habilidades técnicas", len(candidates))

        task_prompt = get_enhanced_batch_candidate_analysis_prompt()

        # Prepare candidates for batch analysis
        candidates_prompt = "\n".join([
            f"[Init Candidate] Candidate {i + 1}:\n{candidate} [End of Candidate]" 
            for i, candidate in enumerate(candidates)
        ])

        # Create user messages
        user_messages = [
            HumanMessage(content=f"Position Description:\n{position}"),
            HumanMessage(content=f"Candidates to Evaluate:\n{candidates_prompt}")
        ]

        # Get schema text for BatchMatchAnalysis
        schema_text = """
 

class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")

class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    ) #
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.") #

class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
        """

        with log_time_block("Batch LLM Analysis"):
            try:
                # Use inference_with_fallback with structured output
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=BatchMatchAnalysis,
                    user_messages=user_messages,
                    model_schema_text=schema_text,
                    models_order=models_order
                )

                if not result:
                    raise RuntimeError("All LLM providers failed")

                # Validate and normalize scores
                for analysis in result.candidates_analysis:
                    try:
                        score = float(analysis.Score)
                        score = max(0.0, min(100.0, score))  # Clamp between 0 and 100
                        analysis.Score = score
                    except (ValueError, TypeError):
                        analysis.Score = 0.0

                logger.info(
                    "Batch analysis completed successfully",
                    extra={
                        "custom_dimensions": {
                            "candidates_count": len(candidates),
                            "analysis_count": len(result.candidates_analysis)
                        }
                    }
                )

                return result

            except Exception as e:
                logger.error("Error in batch analysis: %s", str(e), exc_info=True)
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR))
                raise RuntimeError(f"Batch analysis failed: {str(e)}")


def get_candidate_analysis_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    # 1) Build prompt
    task_prompt = (
        f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach.

        **CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

        **ASSESSMENT METHODOLOGY:**
        1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and candidate's background.
           - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
           - **Same role type**: High compatibility potential (can score 70-100%)
           - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
           - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

        2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

        **COMPATIBILITY CALCULATION RULES:**
        - Final Compatibility = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
        - **MANDATORY CAPS based on role type mismatch:**
          * Major role mismatch (e.g., Developer vs QA): **MAXIMUM 35%** regardless of technology overlap
          * Moderate role mismatch (e.g., Frontend vs Backend): **MAXIMUM 55%**
          * Minor role mismatch (e.g., Junior vs Senior same role): **MAXIMUM 75%**
          * Same role type: Up to 100% based on technology and experience alignment

        **CRITICAL EXAMPLES:**
        - Salesforce Developer → Salesforce QA Position: MAX 35% (same technology, completely different job functions)
        - React Developer → Angular Developer Position: MAX 75% (same role type, different but related technology)
        - Python Developer → Python Developer Position: Up to 100% (perfect alignment)
        - Manual QA → Automation QA Position: MAX 75% (same domain, different approach)

        **ANALYSIS REQUIREMENTS:**
        1. Percentage of compatibility with the position (applying the role type compatibility rules above)
        2. Recommendation: Whether the candidate should move forward (consider both role fit and technology alignment)
        3. Matches Found: Points where candidate meets requirements (distinguish between role-relevant and technology matches)
        4. Missing: Key requirements not evident in candidate's background (prioritize role function gaps over technology gaps)

        I will provide you with the Job Description and CV for analysis.\n\n"""
        f"""Job Description:\n{processed_position}\n\n"""
        f"""CV:\n{candidate_text}\n\n"""
        f"""Return the analysis in strict JSON format according to the CompatibilityEvaluation model, containing the following required fields:
        - compatibilityPercentage (float with one decimal place between 0.0 and 100.0, e.g., 83.7, 42.6 — avoid round numbers or multiples of 5)
        - recommendation (boolean)
        - justification (string - must explain role type compatibility assessment and any caps applied)
        - matchesFound (array of strings)
        - missing (array of strings)

        Requirements:
        1. The field compatibilityPercentage **must be a float, not an integer**, and must include **exactly one decimal place** (e.g., 87.3, 46.8).
        2. **Do not round or simplify** compatibilityPercentage to multiples of 5 or integers.
        3. **MANDATORY**: Apply the role type compatibility caps - do not exceed maximum percentages for role mismatches.
        4. All fields must be filled, even if matchesFound or missing are empty arrays.
        5. Output must be valid JSON — do not include explanations or extra formatting.
        6. In justification, explicitly mention the role type assessment and any compatibility caps applied.
        """
    )
    schema_text = get_related_class_definitions(CompatibilityEvaluation)

    # 2) Call the inference function with the task prompt
    # Using inference_with_fallback to handle multiple models and fallbacks
    analysis = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=CompatibilityEvaluation,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if analysis is None or empty
    if not analysis:
        return None
    return analysis


def get_roles_to_candidates(candidate: dict) -> List[str]:
    # 1) Build prompt
    task_prompt = (
        f"""From the candidate’s resume, output exactly five specific job titles the candidate is qualified to apply for. Return only a JSON array of strings with the job titles (e.g., ["Senior .NET Developer", "..."]). Do not include any explanations or additional fields."""
        f"""\n\nCandidate Resume:\n{candidate}\n\n"""
    )
    # 2) Call the inference function with the task prompt
    roles = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=dict,  # Expecting a simple list of strings
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if roles is None or empty
    if not roles:
        return None
    return roles.get("job_titles", [])


def _invoke_llm_with_fallback(
    messages, default_response, model_order: list = MODELS_CONFIG["default_models_order"]
) -> Dict[str, Any]:
    """
    Attempt to invoke LLM models in order until one succeeds.
    Return default_response if all fail.
    The model is expected to return JSON with required fields.
    """
    print("----------------Evaluating in invoke llm with fallback--------------")
    print(model_order)
    print("=====default_response========")
    print(default_response)
    print("-----------------------------------------------------")
    for model_name in model_order:
        try:
            structured_llm = models_pool[model_name].with_structured_output(
                default_response, method="json_mode"
            )
            result = structured_llm.invoke(messages).model_dump()
            # Validate result is JSON with required keys
            if isinstance(result, dict) and all(
                k in result
                for k in ["LLM_Analysis", "extra_questions", "highlights", "Score"]
            ):
                return result
        except Exception as e:
            print(f"Model {model_name} failed: {e}")

    return default_response


def build_compatibility_evaluation(
    compatibility_percentage: float,
    justification: str,
    matches_found: List[str],
    missing_requirements: List[str]
) -> CompatibilityEvaluation:
    """
    Builds a CompatibilityEvaluation object with the provided parameters.
    """
    return CompatibilityEvaluation(
        compatibilityPercentage=compatibility_percentage,
        recommendation=compatibility_percentage >= 65.0,
        justification=justification,
        matchesFound=matches_found,
        missing=missing_requirements
    )


def extract_position_skills_for_matching(position_description: str, models_order: list = MODELS_CONFIG["default_models_order"]) -> PositionSkillAnalysis:
    """
    Extract and prioritize skills from a job position specifically for candidate matching.

    This function analyzes a job description to identify the most important skills,
    categorizes them by type and importance, and assigns weights for matching calculations.

    Args:
        position_description (str): The job description text to analyze
        models_order (list): Order of LLM models to try for inference

    Returns:
        PositionSkillAnalysis: Structured analysis with prioritized skills and weights

    Example:
        For a "Salesforce QA Engineer" position, this would identify:
        - QA skills as CRITICAL with high weight (core competency)
        - Salesforce tools as IMPORTANT with medium weight (technical tool)
        - Test automation as IMPORTANT (methodology)
    """
    with tracer.start_as_current_span("extract_position_skills_for_matching") as span:
        span.set_attribute("position.description_length", len(position_description))

        logger.info("Extracting prioritized skills for matching from position description")

        task_prompt = """You are an expert recruiter and skills analyst. Analyze the provided job description to identify and prioritize the most important skills for candidate matching.

**CRITICAL INSTRUCTIONS:**

1. **Identify the Primary Role Function**: Determine the core job function (e.g., "Quality Assurance", "Software Development", "Data Analysis")

2. **Extract and Prioritize Skills**: Find 5-8 most important skills, focusing on:
   - Skills that are absolutely essential for success in this role
   - Skills that differentiate good candidates from great ones
   - Both explicitly mentioned skills and those implied by the role requirements

3. **Categorize Each Skill**:
   - **core_competency**: Primary job function skills (e.g., QA testing for QA roles, coding for developer roles)
   - **technical_tool**: Specific technologies, platforms, tools (e.g., Salesforce, Python, AWS)
   - **methodology**: Processes, frameworks, approaches (e.g., Agile, DevOps, Test Automation)
   - **soft_skill**: Communication, leadership, problem-solving
   - **domain_knowledge**: Industry or business domain expertise

4. **Assign Importance Levels**:
   - **critical**: Must-have skills without which the candidate cannot succeed
   - **important**: Highly valuable skills that significantly impact performance
   - **nice_to_have**: Beneficial but not essential skills

5. **Calculate Weights**: Assign numerical weights (0.0 to 1.0) where:
   - critical skills: 0.8-1.0
   - important skills: 0.5-0.7
   - nice_to_have skills: 0.2-0.4
   - Ensure weights reflect the relative importance within the role

**EXAMPLE LOGIC:**
For "Salesforce QA Engineer":
- Quality Assurance (critical, core_competency, weight: 0.9) - This is the primary job function
- Test Automation (critical, methodology, weight: 0.8) - Essential for modern QA
- Salesforce Platform (important, technical_tool, weight: 0.6) - Important but secondary to QA skills
- Manual Testing (important, methodology, weight: 0.5) - Valuable but less critical than automation

**OUTPUT REQUIREMENTS:**
Return a JSON response matching the PositionSkillAnalysis schema with:
- position_title: Extract or infer the job title
- primary_role_function: The core job function
- prioritized_skills: Array of skills with name, importance, category, weight, and description
- skill_weight_rationale: Explanation of the prioritization logic used

**CRITICAL**: Ensure the primary role function skills (core_competency) receive the highest weights, as these are most predictive of job success."""

        user_messages = [
            HumanMessage(content=f"Job Description to Analyze:\n\n{position_description}")
        ]

        with log_time_block("Extract Position Skills for Matching"):
            try:
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=PositionSkillAnalysis,
                    user_messages=user_messages,
                    models_order=models_order
                )

                if not result:
                    logger.error("Failed to extract skills for matching - all models failed")
                    # Return a basic fallback analysis
                    return PositionSkillAnalysis(
                        position_title="Unknown Position",
                        primary_role_function="General",
                        prioritized_skills=[],
                        skill_weight_rationale="Skill extraction failed - using fallback"
                    )

                # Validate and normalize weights
                for skill in result.prioritized_skills:
                    if skill.weight < 0.0:
                        skill.weight = 0.0
                    elif skill.weight > 1.0:
                        skill.weight = 1.0

                logger.info(
                    "Successfully extracted %d prioritized skills for matching",
                    len(result.prioritized_skills),
                    extra={
                        "custom_dimensions": {
                            "position_title": result.position_title,
                            "primary_role_function": result.primary_role_function,
                            "skills_count": len(result.prioritized_skills)
                        }
                    }
                )

                return result

            except Exception as e:
                logger.error("Error extracting skills for matching: %s", str(e), exc_info=True)
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR))

                # Return fallback analysis
                return PositionSkillAnalysis(
                    position_title="Unknown Position",
                    primary_role_function="General",
                    prioritized_skills=[],
                    skill_weight_rationale=f"Skill extraction failed due to error: {str(e)}"
                )


def calculate_skill_match_score(candidate_skills: List[str], position_skills: PositionSkillAnalysis) -> Dict[str, Any]:
    """
    Calculate a weighted skill match score between candidate skills and position requirements.

    Args:
        candidate_skills (List[str]): List of skills extracted from candidate's profile
        position_skills (PositionSkillAnalysis): Prioritized skills from the position

    Returns:
        Dict containing:
        - weighted_score: Overall weighted match score (0.0 to 1.0)
        - skill_matches: Dict of matched skills with their weights
        - missing_critical: List of critical skills not found in candidate
        - match_breakdown: Detailed breakdown by skill category
    """
    if not position_skills.prioritized_skills:
        return {
            "weighted_score": 0.0,
            "skill_matches": {},
            "missing_critical": [],
            "match_breakdown": {}
        }

    # Normalize candidate skills for matching (lowercase, remove extra spaces)
    normalized_candidate_skills = [skill.lower().strip() for skill in candidate_skills]

    skill_matches = {}
    missing_critical = []
    total_weight = 0.0
    matched_weight = 0.0
    category_breakdown = {}

    for position_skill in position_skills.prioritized_skills:
        skill_name = position_skill.name
        skill_weight = position_skill.weight
        skill_importance = position_skill.importance
        skill_category = position_skill.category.value

        total_weight += skill_weight

        # Check for skill match (fuzzy matching)
        is_matched = _is_skill_matched(skill_name, normalized_candidate_skills)

        if is_matched:
            skill_matches[skill_name] = {
                "weight": skill_weight,
                "importance": skill_importance.value,
                "category": skill_category
            }
            matched_weight += skill_weight
        elif skill_importance.value == "critical":
            missing_critical.append(skill_name)

        # Track by category
        if skill_category not in category_breakdown:
            category_breakdown[skill_category] = {
                "total_weight": 0.0,
                "matched_weight": 0.0,
                "skills_count": 0,
                "matched_count": 0
            }

        category_breakdown[skill_category]["total_weight"] += skill_weight
        category_breakdown[skill_category]["skills_count"] += 1

        if is_matched:
            category_breakdown[skill_category]["matched_weight"] += skill_weight
            category_breakdown[skill_category]["matched_count"] += 1

    # Calculate overall weighted score
    weighted_score = matched_weight / total_weight if total_weight > 0 else 0.0

    return {
        "weighted_score": weighted_score,
        "skill_matches": skill_matches,
        "missing_critical": missing_critical,
        "match_breakdown": category_breakdown
    }


def _is_skill_matched(position_skill: str, candidate_skills: List[str]) -> bool:
    """
    Check if a position skill is matched by any candidate skill using fuzzy matching.

    Args:
        position_skill (str): The skill from the position requirements
        candidate_skills (List[str]): Normalized candidate skills (lowercase)

    Returns:
        bool: True if skill is matched, False otherwise
    """
    position_skill_normalized = position_skill.lower().strip()

    # Direct match
    if position_skill_normalized in candidate_skills:
        return True

    # Partial match - check if position skill is contained in any candidate skill
    for candidate_skill in candidate_skills:
        if position_skill_normalized in candidate_skill or candidate_skill in position_skill_normalized:
            return True

    # Common skill synonyms and variations
    skill_synonyms = {
        "quality assurance": ["qa", "testing", "quality control", "qc"],
        "software testing": ["qa", "quality assurance", "testing"],
        "test automation": ["automated testing", "automation testing", "test automation"],
        "manual testing": ["manual qa", "manual test"],
        "python": ["python programming", "python development"],
        "javascript": ["js", "javascript programming"],
        "salesforce": ["sfdc", "salesforce platform", "salesforce.com"],
        "agile": ["agile methodology", "agile development", "scrum"],
        "devops": ["dev ops", "development operations"],
    }

    # Check synonyms
    for canonical_skill, synonyms in skill_synonyms.items():
        if canonical_skill in position_skill_normalized:
            for synonym in synonyms:
                if any(synonym in candidate_skill for candidate_skill in candidate_skills):
                    return True

        if any(synonym in position_skill_normalized for synonym in synonyms):
            if canonical_skill in " ".join(candidate_skills):
                return True

    return False


def normalize_skill_weights(position_skills: PositionSkillAnalysis) -> PositionSkillAnalysis:
    """
    Normalize skill weights to ensure they sum to 1.0 while maintaining relative importance.

    Args:
        position_skills (PositionSkillAnalysis): Position skills analysis to normalize

    Returns:
        PositionSkillAnalysis: Updated analysis with normalized weights
    """
    if not position_skills.prioritized_skills:
        return position_skills

    total_weight = sum(skill.weight for skill in position_skills.prioritized_skills)

    if total_weight == 0:
        # If all weights are 0, distribute equally
        equal_weight = 1.0 / len(position_skills.prioritized_skills)
        for skill in position_skills.prioritized_skills:
            skill.weight = equal_weight
    else:
        # Normalize to sum to 1.0
        for skill in position_skills.prioritized_skills:
            skill.weight = skill.weight / total_weight

    return position_skills


def get_skill_aware_candidate_analysis_prompt(position_skills: PositionSkillAnalysis) -> str:
    """
    Generate a skill-aware prompt template that uses prioritized skills for candidate evaluation.

    Args:
        position_skills (PositionSkillAnalysis): Prioritized skills analysis for the position

    Returns:
        str: Enhanced prompt template that incorporates skill priorities
    """
    # Build skill priority information for the prompt
    skills_info = []
    for skill in position_skills.prioritized_skills:
        importance_desc = {
            "critical": "CRITICAL (Must-have)",
            "important": "IMPORTANT (Highly valuable)",
            "nice_to_have": "NICE-TO-HAVE (Beneficial)"
        }.get(skill.importance.value, skill.importance.value)

        category_desc = skill.category.value.replace("_", " ").title()

        skills_info.append(
            f"• **{skill.name}** ({importance_desc}, {category_desc}, Weight: {skill.weight:.2f})"
            + (f" - {skill.description}" if skill.description else "")
        )

    skills_section = "\n".join(skills_info) if skills_info else "No specific skills identified."

    return f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using an intelligent skill prioritization approach.

**POSITION ANALYSIS:**
- **Position Title:** {position_skills.position_title}
- **Primary Role Function:** {position_skills.primary_role_function}
- **Skill Prioritization Rationale:** {position_skills.skill_weight_rationale}

**PRIORITIZED SKILLS FOR THIS POSITION:**
{skills_section}

**ASSESSMENT METHODOLOGY:**

1. **Skill-Based Evaluation (PRIMARY - 70% weight)**: Evaluate the candidate against the prioritized skills above:
   - **critical skills**: Must be present for candidate viability (if missing, maximum score is 40%)
   - **important skills**: Significantly impact the overall score
   - **nice_to_have skills**: Provide bonus points but don't penalize if missing
   - Weight each skill match according to the specified weights above

2. **Role Function Alignment (SECONDARY - 30% weight)**: Assess overall alignment with the primary role function

**CRITICAL MATCHING RULES:**
- **Missing critical skills**: If any critical skill is completely absent, cap the maximum score at 40%
- **Skill weight application**: Use the specified weights to calculate the skill match portion
- **Evidence requirement**: Only count skills where you can find clear evidence in the candidate's background
- **Partial matches**: Give partial credit for related or similar skills, but be conservative

**SCORING CALCULATION:**
1. Calculate weighted skill match score using the skill weights above
2. Assess role function alignment (0-100%)
3. Final Score = (Skill Match Score × 0.7) + (Role Function Score × 0.3)
4. Apply any caps for missing critical skills

**OUTPUT REQUIREMENTS:**
Return a JSON response with EXACTLY this structure:

{{
  "LLM_Analysis": {{
    "reason": "Detailed explanation including skill-by-skill analysis and how weights were applied",
    "skill_match_analysis": {{"skill_name": "explanation for each matched skill with evidence"}},
    "skill_not_matched": ["list of skills from the prioritized list that were not found"]
  }},
  "extra_questions": ["list of additional questions for further evaluation"],
  "highlights": ["list of key points about the candidate's suitability"],
  "Score": 85.5
}}

**CRITICAL REQUIREMENTS:**
- The Score field must be at the TOP LEVEL, not inside LLM_Analysis
- In the reason field, explicitly mention how skill weights were applied and any score caps
- Only include skills from the prioritized list above in skill_match_analysis and skill_not_matched
- Be specific about evidence found for each skill match
- Apply the scoring methodology exactly as specified above"""


def get_skill_aware_batch_candidate_analysis_prompt(position_skills: PositionSkillAnalysis) -> str:
    """
    Generate a skill-aware batch prompt template for evaluating multiple candidates.

    Args:
        position_skills (PositionSkillAnalysis): Prioritized skills analysis for the position

    Returns:
        str: Enhanced batch prompt template that incorporates skill priorities
    """
    # Build skill priority information for the prompt
    skills_info = []
    for skill in position_skills.prioritized_skills:
        importance_desc = {
            "critical": "CRITICAL (Must-have)",
            "important": "IMPORTANT (Highly valuable)",
            "nice_to_have": "NICE-TO-HAVE (Beneficial)"
        }.get(skill.importance.value, skill.importance.value)

        category_desc = skill.category.value.replace("_", " ").title()

        skills_info.append(
            f"• **{skill.name}** ({importance_desc}, {category_desc}, Weight: {skill.weight:.2f})"
            + (f" - {skill.description}" if skill.description else "")
        )

    skills_section = "\n".join(skills_info) if skills_info else "No specific skills identified."

    return f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and multiple candidates' CVs using an intelligent skill prioritization approach.

**POSITION ANALYSIS:**
- **Position Title:** {position_skills.position_title}
- **Primary Role Function:** {position_skills.primary_role_function}
- **Skill Prioritization Rationale:** {position_skills.skill_weight_rationale}

**PRIORITIZED SKILLS FOR THIS POSITION:**
{skills_section}

**ASSESSMENT METHODOLOGY FOR EACH CANDIDATE:**

1. **Skill-Based Evaluation (PRIMARY - 70% weight)**: Evaluate each candidate against the prioritized skills above:
   - **critical skills**: Must be present for candidate viability (if missing, maximum score is 40%)
   - **important skills**: Significantly impact the overall score
   - **nice_to_have skills**: Provide bonus points but don't penalize if missing
   - Weight each skill match according to the specified weights above

2. **Role Function Alignment (SECONDARY - 30% weight)**: Assess overall alignment with the primary role function

**CRITICAL MATCHING RULES:**
- **Missing critical skills**: If any critical skill is completely absent, cap the maximum score at 40%
- **Skill weight application**: Use the specified weights to calculate the skill match portion
- **Evidence requirement**: Only count skills where you can find clear evidence in the candidate's background
- **Partial matches**: Give partial credit for related or similar skills, but be conservative

**OUTPUT REQUIREMENTS:**
Return a JSON object with "candidates_analysis" array containing analysis for each candidate in order.

Each candidate analysis must have EXACTLY this structure:

{{
  "candidates_analysis": [
    {{
      "LLM_Analysis": {{
        "reason": "Detailed explanation including skill-by-skill analysis and how weights were applied",
        "skill_match_analysis": {{"skill_name": "explanation for each matched skill with evidence"}},
        "skill_not_matched": ["list of skills from the prioritized list that were not found"]
      }},
      "extra_questions": ["list of additional questions"],
      "highlights": ["list of key points about suitability"],
      "Score": 85.5
    }}
  ]
}}

**CRITICAL REQUIREMENTS:**
- The Score field must be at the TOP LEVEL of each candidate analysis, not inside LLM_Analysis
- In each reason field, explicitly mention how skill weights were applied and any score caps
- Only include skills from the prioritized list above in skill_match_analysis and skill_not_matched
- Be specific about evidence found for each skill match
- Apply the scoring methodology exactly as specified above for each candidate"""


def evaluate_candidate_with_skill_priority(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> dict:
    """
    Enhanced candidate evaluation that first extracts prioritized skills from the position,
    then performs skill-aware matching.

    Args:
        candidate (str): Candidate description/CV text
        position (str): Position description text
        models_order (list): Order of LLM models to try

    Returns:
        dict: Analysis response with skill-aware evaluation
    """
    with tracer.start_as_current_span("evaluate_candidate_with_skill_priority") as span:
        span.set_attribute("eval.candidate_length", len(candidate))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluating candidate with skill priority analysis")

        try:
            # Step 1: Extract prioritized skills from position
            with log_time_block("Extract Position Skills"):
                position_skills = extract_position_skills_for_matching(position, models_order)

            span.set_attribute("eval.skills_extracted", len(position_skills.prioritized_skills))

            # Step 2: Generate skill-aware prompt
            system_prompt = get_skill_aware_candidate_analysis_prompt(position_skills)

            # Step 3: Evaluate candidate using skill-aware prompt
            messages = [
                {"role": "user", "content": f"Candidate description: {candidate}"},
                {"role": "user", "content": f"Position description: {position}"},
            ]

            with log_time_block("Skill-Aware Candidate Evaluation"):
                analysis_response = inference_with_fallback(
                    task_prompt=system_prompt,
                    model_schema=PositionCandidateAnalysis,
                    user_messages=messages,
                    models_order=models_order
                )

            if not analysis_response:
                logger.error("Skill-aware LLM analysis failed for candidate evaluation")
                return {
                    "LLM_Analysis": {
                        "reason": "Skill-aware LLM analysis failed",
                        "skill_match_analysis": {},
                        "skill_not_matched": []
                    },
                    "extra_questions": [],
                    "highlights": [],
                    "Score": 0.0,
                    "position_skills_analysis": position_skills.model_dump()
                }

            analysis_response = analysis_response.model_dump()
            score = analysis_response.get("Score", 0.0)
            try:
                score = float(score)
            except ValueError:
                score = 0.0

            # Add position skills analysis to response for transparency
            analysis_response["position_skills_analysis"] = position_skills.model_dump()

            logger.info(
                "Skill-aware candidate evaluation completed",
                extra={
                    "custom_dimensions": {
                        "score": score,
                        "position_title": position_skills.position_title,
                        "skills_count": len(position_skills.prioritized_skills)
                    }
                }
            )

            return analysis_response

        except Exception as e:
            logger.error("Error in skill-aware candidate evaluation: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback to regular evaluation
            logger.info("Falling back to regular candidate evaluation")
            return evaluate_candidate(candidate, position, models_order)


def evaluate_candidates_batch_with_skill_priority(candidates: List[str], position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> BatchMatchAnalysis:
    """
    Enhanced batch candidate evaluation that first extracts prioritized skills from the position,
    then performs skill-aware matching for multiple candidates.

    Args:
        candidates (List[str]): List of candidate descriptions/CV texts
        position (str): Position description text
        models_order (list): Order of LLM models to try

    Returns:
        BatchMatchAnalysis: Batch analysis with skill-aware evaluation
    """
    with tracer.start_as_current_span("evaluate_candidates_batch_with_skill_priority") as span:
        span.set_attribute("eval.candidates_count", len(candidates))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluating %d candidates with skill priority analysis in batch mode", len(candidates))

        try:
            # Step 1: Extract prioritized skills from position
            with log_time_block("Extract Position Skills for Batch"):
                position_skills = extract_position_skills_for_matching(position, models_order)

            span.set_attribute("eval.skills_extracted", len(position_skills.prioritized_skills))

            # Step 2: Generate skill-aware batch prompt
            task_prompt = get_skill_aware_batch_candidate_analysis_prompt(position_skills)

            # Step 3: Prepare candidates for batch analysis
            candidates_prompt = "\n".join([
                f"[Init Candidate] Candidate {i + 1}:\n{candidate} [End of Candidate]"
                for i, candidate in enumerate(candidates)
            ])

            # Create user messages
            user_messages = [
                HumanMessage(content=f"Position Description:\n{position}"),
                HumanMessage(content=f"Candidates to Evaluate:\n{candidates_prompt}")
            ]

            # Get schema text for BatchMatchAnalysis
            schema_text = """
class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")

class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    )
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.")

class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
            """

            with log_time_block("Skill-Aware Batch LLM Analysis"):
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=BatchMatchAnalysis,
                    user_messages=user_messages,
                    model_schema_text=schema_text,
                    models_order=models_order
                )

                if not result:
                    logger.error("Skill-aware batch analysis failed - falling back to regular batch analysis")
                    return evaluate_candidates_batch(candidates, position, models_order)

                # Validate and normalize scores
                for analysis in result.candidates_analysis:
                    try:
                        score = float(analysis.Score)
                        score = max(0.0, min(100.0, score))  # Clamp between 0 and 100
                        analysis.Score = score
                    except (ValueError, TypeError):
                        analysis.Score = 0.0

                # Add position skills analysis to the result for transparency
                # Note: We add this as a custom attribute to the result object
                result.position_skills_analysis = position_skills.model_dump()

                logger.info(
                    "Skill-aware batch analysis completed successfully",
                    extra={
                        "custom_dimensions": {
                            "candidates_count": len(candidates),
                            "analysis_count": len(result.candidates_analysis),
                            "position_title": position_skills.position_title,
                            "skills_count": len(position_skills.prioritized_skills)
                        }
                    }
                )

                return result

        except Exception as e:
            logger.error("Error in skill-aware batch analysis: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback to regular batch evaluation
            logger.info("Falling back to regular batch candidate evaluation")
            return evaluate_candidates_batch(candidates, position, models_order)


def get_candidate_analysis_with_skill_priority(candidate_text: str, processed_position: str, models_order: list = MODELS_CONFIG["default_models_order"]) -> CompatibilityEvaluation:
    """
    Enhanced custom prompt evaluation that first extracts prioritized skills from the position,
    then performs skill-aware compatibility evaluation.

    Args:
        candidate_text (str): Candidate's CV/resume text
        processed_position (str): Processed position description
        models_order (list): Order of LLM models to try

    Returns:
        CompatibilityEvaluation: Enhanced compatibility evaluation with skill prioritization
    """
    with tracer.start_as_current_span("get_candidate_analysis_with_skill_priority") as span:
        span.set_attribute("eval.candidate_length", len(candidate_text))
        span.set_attribute("eval.position_length", len(processed_position))

        logger.info("Performing skill-aware compatibility evaluation")

        try:
            # Step 1: Extract prioritized skills from position
            with log_time_block("Extract Position Skills for Custom Analysis"):
                position_skills = extract_position_skills_for_matching(processed_position, models_order)

            span.set_attribute("eval.skills_extracted", len(position_skills.prioritized_skills))

            # Step 2: Build skill-aware custom prompt
            skills_info = []
            for skill in position_skills.prioritized_skills:
                importance_desc = {
                    "critical": "CRITICAL (Must-have)",
                    "important": "IMPORTANT (Highly valuable)",
                    "nice_to_have": "NICE-TO-HAVE (Beneficial)"
                }.get(skill.importance.value, skill.importance.value)

                category_desc = skill.category.value.replace("_", " ").title()

                skills_info.append(
                    f"• **{skill.name}** ({importance_desc}, {category_desc}, Weight: {skill.weight:.2f})"
                    + (f" - {skill.description}" if skill.description else "")
                )

            skills_section = "\n".join(skills_info) if skills_info else "No specific skills identified."

            task_prompt = f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using intelligent skill prioritization.

**POSITION ANALYSIS:**
- **Position Title:** {position_skills.position_title}
- **Primary Role Function:** {position_skills.primary_role_function}
- **Skill Prioritization Rationale:** {position_skills.skill_weight_rationale}

**PRIORITIZED SKILLS FOR THIS POSITION:**
{skills_section}

**ASSESSMENT METHODOLOGY:**

1. **Skill-Based Evaluation (PRIMARY - 70% weight)**: Evaluate the candidate against the prioritized skills above:
   - **critical skills**: Must be present for candidate viability (if missing, maximum compatibility is 40%)
   - **important skills**: Significantly impact the overall compatibility
   - **nice_to_have skills**: Provide bonus points but don't penalize if missing
   - Weight each skill match according to the specified weights above

2. **Role Function Alignment (SECONDARY - 30% weight)**: Assess overall alignment with the primary role function

**CRITICAL MATCHING RULES:**
- **Missing critical skills**: If any critical skill is completely absent, cap the maximum compatibility at 40%
- **Skill weight application**: Use the specified weights to calculate the skill match portion
- **Evidence requirement**: Only count skills where you can find clear evidence in the candidate's background
- **Partial matches**: Give partial credit for related or similar skills, but be conservative

**COMPATIBILITY CALCULATION:**
1. Calculate weighted skill match score using the skill weights above
2. Assess role function alignment (0-100%)
3. Final Compatibility = (Skill Match Score × 0.7) + (Role Function Score × 0.3)
4. Apply any caps for missing critical skills

**ANALYSIS REQUIREMENTS:**
1. Compatibility percentage with the position (applying the skill prioritization rules above)
2. Recommendation: Whether the candidate should move forward (consider both skill fit and role alignment)
3. Matches Found: Points where candidate meets requirements (focus on prioritized skills)
4. Missing: Key requirements not evident in candidate's background (prioritize critical skill gaps)

Job Description:
{processed_position}

CV:
{candidate_text}

Return the analysis in strict JSON format according to the CompatibilityEvaluation model, containing the following required fields:
- compatibilityPercentage (float with one decimal place between 0.0 and 100.0, e.g., 83.7, 42.6 — avoid round numbers or multiples of 5)
- recommendation (boolean)
- justification (string - must explain skill prioritization assessment and any caps applied)
- matchesFound (array of strings - focus on prioritized skills)
- missing (array of strings - focus on critical and important skills)

Requirements:
1. The field compatibilityPercentage **must be a float, not an integer**, and must include **exactly one decimal place** (e.g., 87.3, 46.8).
2. **Do not round or simplify** compatibilityPercentage to multiples of 5 or integers.
3. **MANDATORY**: Apply the skill prioritization caps - do not exceed maximum percentages for missing critical skills.
4. All fields must be filled, even if matchesFound or missing are empty arrays.
5. Output must be valid JSON — do not include explanations or extra formatting.
6. In justification, explicitly mention the skill prioritization assessment and any compatibility caps applied.
"""

            schema_text = get_related_class_definitions(CompatibilityEvaluation)

            # Step 3: Call the inference function with the skill-aware prompt
            with log_time_block("Skill-Aware Custom Analysis"):
                analysis = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=CompatibilityEvaluation,
                    user_messages=[HumanMessage(content="")],  # no extra user message needed
                    model_schema_text=schema_text,
                    models_order=models_order,
                )

            if not analysis:
                logger.error("Skill-aware custom analysis failed - falling back to regular analysis")
                return get_candidate_analysis_custom_prompt(candidate_text, processed_position)

            logger.info(
                "Skill-aware custom analysis completed",
                extra={
                    "custom_dimensions": {
                        "compatibility_percentage": analysis.compatibilityPercentage,
                        "position_title": position_skills.position_title,
                        "skills_count": len(position_skills.prioritized_skills)
                    }
                }
            )

            return analysis

        except Exception as e:
            logger.error("Error in skill-aware custom analysis: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback to regular custom analysis
            logger.info("Falling back to regular custom analysis")
            return get_candidate_analysis_custom_prompt(candidate_text, processed_position)